import { createSelector } from '@reduxjs/toolkit';
import { selectSelf } from 'shared/lib';
import { specialPermissionsStore } from '..';
import { slice } from './reducers';

export const permissionsSelector = selectSelf(slice.name);

export const reportPermissionsSelector = createSelector(
  permissionsSelector,
  (permissions) => permissions.report,
);

export const wildcardPermissionsSelector = createSelector(
  permissionsSelector,
  (permissions) => permissions.wildcard,
);

export const isFullFilesAccessSelector = createSelector(
  wildcardPermissionsSelector,
  ({ permissions }) =>
    (permissions || []).includes(
      specialPermissionsStore.enums.WildcardPermissions.FullAccess,
    ),
);
