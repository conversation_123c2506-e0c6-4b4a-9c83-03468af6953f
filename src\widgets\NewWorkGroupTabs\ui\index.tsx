import type { FC } from 'react';
import {
  NewWorkGroupTabsProps,
  NWGStore,
  NWGConfig,
  NWGLib,
} from 'widgets/NewWorkGroupTabs';
import { customColumnFiltersStore } from 'features/CustomColumnFilters';
import { TabHeader } from 'features/TabHeader';
import { usePopupsToggle } from 'shared/model';
import { useTableSelection } from 'shared/model/useTableSelection';
import { ApiContainer, ButtonsContainer } from 'shared/ui';
import { MainTabs } from './MainTabs';
import { Popups } from './Popups';
import styles from './styles.module.scss';
import { TabsOrTable } from './TabsOrTable';

export const NewWorkGroupTabs: FC<NewWorkGroupTabsProps> = ({
  additionalParams,
}) => {
  const cabinetId = additionalParams?.auditCode || '';
  const { isInspectionSet } = NWGLib;
  const [popup, togglePopup] = usePopupsToggle(NWGConfig.popupsState);
  const [rowData, handleRow, resetRow] = NWGStore.hooks.useRowAdditional();

  /* ----------------------------------------------------
   *                      Экшены
   ---------------------------------------------------- */
  const [handleAdditional, additional] = NWGStore.hooks.useTableAction();
  const [selectedData, { clearSelection, handleSelection }] =
    useTableSelection();
  const [onSubmit, filters, resetFilters, onReset] =
    customColumnFiltersStore.hooks.useColumnFilter();

  const [sortOrder, sortOrderNested, handleSort, resetSort] =
    NWGStore.hooks.useColumnSorter();
  const [
    activeNestedKey,
    handleNestedTab,
    nestedTableEndpoint,
    nestedTable,
    handleTableData,
  ] = NWGStore.hooks.useNestedTabs(resetSort, clearSelection, resetFilters);
  const [activeKey, tableEndpoint, handleChangeTab] =
    NWGStore.hooks.useTabsActions(
      clearSelection,
      handleNestedTab,
      resetFilters,
      resetSort,
    );
  const [total, page, handlePage, handleTotal] =
    NWGStore.hooks.useTablePagination();

  /* ----------------------------------------------------
   *                      Данные
   ---------------------------------------------------- */
  const [
    permissionsForCabinet,
    permissions,
    specialPermissionsStatuses,
    isFullFileAccess,
  ] = NWGStore.hooks.useCabinetPermissions(cabinetId);
  const [
    header,
    getHeader,
    isSendRequestEnabledByCabinetStatus,
    isCanEditProfile,
  ] = NWGStore.hooks.useGetHeader(
    additionalParams,
    permissionsForCabinet.isResEnd,
    permissions,
  );
  const [tabs, counter, getCounter] = NWGStore.hooks.useMainTabs(
    cabinetId,
    handleChangeTab,
    permissionsForCabinet.isResEnd,
    permissions,
  );
  const [tableData, getTableData] = NWGStore.hooks.useMainContent(
    tableEndpoint,
    cabinetId,
    handleNestedTab,
    clearSelection,
    handleTotal,
    handlePage,
    additional,
    filters.main,
    sortOrder,
    permissionsForCabinet.isResEnd,
    permissions,
  );
  const [nestedData, getNestedData, isNestedLazyTable, getNestedLazyTable] =
    NWGStore.hooks.useNestedData(
      nestedTableEndpoint,
      cabinetId,
      handleTotal,
      handlePage,
      handleTableData,
      sortOrderNested,
      filters.nested,
    );

  /* ----------------------------------------------------
   *                      Кнопки
   ---------------------------------------------------- */
  const additionalButtons = NWGStore.hooks.useAdditionalButtons(
    togglePopup,
    permissions,
  );
  const tableButtons = NWGStore.hooks.useTableButtons(
    tableEndpoint,
    togglePopup,
    selectedData.rows,
    cabinetId,
    () => {
      clearSelection();
      getTableData();
    },
    Array.isArray(tableData.data) ? [] : tableData.data?.rows || [],
    handleAdditional,
    getCounter,
    permissions,
  );
  const nestedButtons = NWGStore.hooks.useNestedButtons(
    nestedTableEndpoint,
    togglePopup,
    selectedData.rows,
    () => {
      clearSelection();
      getNestedData();
    },
    cabinetId,
    isInspectionSet(header.data?.tabHeaders || []),
    permissions,
    isSendRequestEnabledByCabinetStatus,
  );
  const switchRequestButtons = NWGStore.hooks.useRequestInternalStatusButtons(
    nestedTableEndpoint,
    selectedData.rows,
    getNestedData,
  );
  const refetchButton = NWGStore.hooks.useRefetchButton(() => {
    clearSelection();
    if (Array.isArray(tableData.data)) {
      getNestedData(page);
    } else if (tableData.data !== null && tableData.data.columns.length !== 0) {
      getTableData(page);
    }
  });

  return (
    <ApiContainer
      error={
        permissionsForCabinet.error ||
        (specialPermissionsStatuses.isError &&
          'Ошибка при получении прав доступа')
      }
      isPending={
        permissionsForCabinet.isPending || specialPermissionsStatuses.isLoading
      }
    >
      <div className={styles.tab}>
        <TabHeader
          headerData={header.data?.tabHeaders || []}
          editCallback={() => togglePopup('inspection')}
          error={header.error}
          isCanEdit={isCanEditProfile}
          isPending={header.isPending}
        />

        <ButtonsContainer
          buttons={additionalButtons}
          className={styles.additionalButtons}
        />

        <MainTabs
          tabs={tabs}
          counter={counter}
          activeKey={activeKey}
          handleChangeTab={handleChangeTab}
        />

        <TabsOrTable
          {...(isNestedLazyTable && { getNestedLazyTable })}
          clearSelection={clearSelection}
          handleRowData={handleRow}
          permissions={permissions}
          columnFilters={{
            filters: Array.isArray(tableData.data)
              ? filters.nested
              : filters.main,
            onSubmit,
            onReset,
          }}
          columnSorter={{
            sortOrder: Array.isArray(tableData.data)
              ? sortOrderNested
              : sortOrder,
            handleSort,
          }}
          getData={Array.isArray(tableData.data) ? getNestedData : getTableData}
          total={total}
          currentPage={page}
          data={tableData.data || []}
          activeEndpoint={
            Array.isArray(tableData.data) ? nestedTableEndpoint : tableEndpoint
          }
          additionalButtons={
            Array.isArray(tableData.data)
              ? [...nestedButtons, ...switchRequestButtons, ...refetchButton]
              : [...tableButtons, ...refetchButton]
          }
          handleTab={handleNestedTab}
          nestedActiveKey={activeNestedKey}
          handleSelect={handleSelection}
          nestedData={
            Array.isArray(tableData.data)
              ? ({
                  ...nestedData?.data,
                  columns: nestedTable.columns,
                  rows: nestedTable.rows,
                } as TableColumnsAndRowsWithPagination)
              : null
          }
          isPending={tableData.isPending || nestedData.isPending}
          togglePopup={togglePopup}
          selectedTableKeys={selectedData.keys}
          isShowSelects={
            !NWGConfig.constants.NO_SELECTS_ENDPOINTS.includes(
              nestedTableEndpoint || tableEndpoint,
            )
          }
        />

        <Popups
          isFullFilesAccess={isFullFileAccess}
          permissions={permissions}
          rowAdditional={{ data: rowData, reset: resetRow }}
          refetch={() => {
            clearSelection();
            if (Array.isArray(tableData.data)) {
              getNestedData(page);
            } else {
              getTableData(page);
            }
          }}
          cabinetId={cabinetId}
          popup={popup}
          refetchHeader={getHeader}
          togglePopup={togglePopup}
        />
      </div>
    </ApiContainer>
  );
};
