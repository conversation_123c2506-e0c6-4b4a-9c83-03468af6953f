export type DownloadReportProps = DownloadReportInnerProps & {
  isOpened: boolean;
  onClose: Callback;
  title: Title;
};

export interface DownloadReportInnerProps {
  onOutputToFileClick: (
    values: typeof import('widgets/WGDownloadReport').WGReportsModel.defaultValues,
  ) => Promise<void>;
  params: {
    date?: boolean;
    reportTypeKey?: string;
    select?: boolean;
  };
}

export type ReportTypes = 'xlsx' | 'docx' | 'html' | 'rtf' | 'pdf';

export type DefaultValue = Record<
  'dateTo' | 'dateFrom' | 'ext' | 'type',
  string
>;
