import { ReportTypes } from '..';

export const reportTypes: Record<ReportTypes, string> = {
  xlsx: 'MS Excel',
  docx: 'MS Word',
  html: 'html',
  rtf: 'rtf',
};

/* TODO: вынести на бек когда появятся номральыне беки, сейчас таких нет */
export const reportSelects = {
  execution_supervising_nfo: [
    {
      title:
        'Отчет о проведенных проверках кредитных организаций (краткая форма)',
      key: '1',
      typeNameKey: 'execution_supervising_report_completed_audits',
    },
    {
      title: 'Отчет о проведенных проверках кредитных организаций (все поля)',
      key: '2',
      typeNameKey: 'execution_supervising_report_audits_wide',
    },
  ],
  execution_control_nfo: [
    {
      title: 'Отчет по проведенным проверкам поднадзорных организаций',
      key: '1',
      typeNameKey: 'execution_control_nfo_report_completed_audits',
    },
    {
      title:
        'Отчет по составу и содержанию документов по проверкам поднадзорных организаций',
      key: '2',
      typeNameKey: 'execution_control_nfo_report_audit_files',
    },
  ],
  execution_persons: [
    {
      title: 'Отчет по проведенным проверкам поднадзорных лиц',
      key: '1',
      typeNameKey: 'execution_control_pl_report_completed_audits',
    },
    {
      title: 'Отчет по проведенным проверкам поднадзорных лиц (все поля)',
      key: '2',
      typeNameKey: 'execution_control_pl_report_audit_wide_form',
    },
  ],
  offer_summary_plan_nfo: [
    {
      title: 'Отчет по неучтенным предложениям в проект Сводного плана',
      key: '1',
      typeNameKey: 'offer_summary_plan_nfo_report_declined_offers',
    },
  ],
  offer_associated_plan: [
    {
      title: 'Отчет по неучтенным предложениям в проект Единого плана',
      key: '1',
      typeNameKey: 'offer_associated_plan_report_declined_offers',
    },
  ],
  summary_plan_unplanned_audit: [
    {
      title: 'Сводный план',
      key: '1',
      typeNameKey: 'summary_plan_report',
    },
    {
      title: 'Выписка  из Сводного плана',
      key: '2',
      typeNameKey: 'summary_plan_extract_report',
    },
    {
      title: 'Выписка для Департаментов из Сводного плана',
      key: '3',
      typeNameKey: 'summary_plan_report_department',
    },
    {
      title: 'Выписка для МИ из Сводного плана ',
      key: '4',
      typeNameKey: 'summary_plan_report_mi',
    },
  ],
  associated_plan_unplanned_audit: [
    {
      title: 'Единый план',
      key: '1',
      typeNameKey: 'summary_plan_nfo_report_main',
    },
    {
      title: 'Выписка из Единого плана',
      key: '2',
      typeNameKey: 'summary_plan_nfo_extract_report_main',
    },
    {
      title: 'Выписка из Единого плана (межрегиональные проверки)',
      key: '3',
      typeNameKey: 'summary_plan_nfo_report_interregional_sublist',
    },
  ],
  joined_audit_plan: [
    {
      title: 'План проверок поднадзорных лиц',
      key: '1',
      typeNameKey: 'joined_plan_report_appendix_a',
    },
    {
      title: 'План проверок поднадзорных лиц с дополнительной информацией',
      key: '2',
      typeNameKey: 'joined_plan_report_appendix_b',
    },
    {
      title: 'Выписка из Плана проверок поднадзорных лиц',
      key: '3',
      typeNameKey: 'joined_plan_report_appendix_v',
    },
    {
      title:
        'Выписка из Плана проверок поднадзорных лиц  с дополнительной информацией',
      key: '4',
      typeNameKey: 'joined_plan_report_appendix_g',
    },
  ],
  unplanned_audits: [
    {
      title: 'Информация о внеплановых проверках поднадзорных лиц',
      key: '1',
      typeNameKey: 'joined_plan_unplanned_checks',
    },
    {
      title:
        'Информация о внеплановых проверках поднадзорных лиц с дополнительной информацией',
      key: '2',
      typeNameKey: 'joined_plan_unplanned_checks_with_additional_information',
    },
  ],
  krg4: [
    {
      title: 'Перечень КРГ (статусы и состояние)',
      key: '1',
      typeNameKey: 'workgroup_cabinet_management_report_wgc_statuses_state',
    },
    {
      title: 'Сведения о состоянии квот и резервов КРГ и ЛК УИО',
      key: '2',
      typeNameKey: 'workgroup_cabinet_management_report_quotas_reserves',
    },
  ],
  krg3: [
    {
      title: 'Сведения о состоянии информационного взаимодействия КРГ и САДД',
      key: '1',
      typeNameKey: 'workgroup_cabinet_report_sadd_interaction',
    },
    {
      title: 'Сведения о пользователях, имеющих доступ к КРГ и их полномочиях',
      key: '2',
      typeNameKey: 'workgroup_cabinet_report_users',
    },
  ],
  notification_list: [
    {
      title: 'Сведения о состоянии информационного взаимодействия КРГ с ЛК УИО',
      key: '3',
      typeNameKey:
        'workgroup_cabinet_notification_list_report_participant_interaction',
    },
  ],
} as const;
