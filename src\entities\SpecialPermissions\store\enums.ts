export enum ReportPermissions {
  JoinedPlanUnplannedChecksWithAdditionalInformation = 'joined_plan_unplanned_checks_with_additional_information',
  JoinedPlanReportAppendixA = 'joined_plan_report_appendix_a',
  JoinedPlanReportAppendixB = 'joined_plan_report_appendix_b',
  SummaryPlanNfoReportInterregionalSublist = 'summary_plan_nfo_report_interregional_sublist',
  SummaryPlanExtractReport = 'summary_plan_extract_report',
  SummaryPlanReportMi = 'summary_plan_report_mi',
  JoinedPlanReportAppendixG = 'joined_plan_report_appendix_g',
  SummaryPlanReportDepartment = 'summary_plan_report_department',
  SummaryPlanReport = 'summary_plan_report',
  SummaryPlanNfoReportMain = 'summary_plan_nfo_report_main',
  ExecutionControlNfoReportCompletedAudits = 'execution_control_nfo_report_completed_audits',
  JoinedPlanUnplannedChecks = 'joined_plan_unplanned_checks',
  ExecutionSupervisingReportCompletedAudits = 'execution_supervising_report_completed_audits',
  OfferSummaryPlanNfoReportDeclinedOffers = 'offer_summary_plan_nfo_report_declined_offers',
  ExecutionControlPlReportCompletedAudits = 'execution_control_pl_report_completed_audits',
  ExecutionSupervisingReportAuditsWide = 'execution_supervising_report_audits_wide',
  ExecutionControlPlReportAuditWideForm = 'execution_control_pl_report_audit_wide_form',
  OfferAssociatedPlanReportDeclinedOffers = 'offer_associated_plan_report_declined_offers',
  ExecutionControlNfoReportAuditFiles = 'execution_control_nfo_report_audit_files',
  JoinedPlanReportAppendixV = 'joined_plan_report_appendix_v',
  SummaryPlanNfoExtractReportMain = 'summary_plan_nfo_extract_report_main',
  WorkgroupCabinetReportSaddInteraction = 'workgroup_cabinet_report_sadd_interaction',
  WorkgroupCabinetReportUsers = 'workgroup_cabinet_report_users',
  WorkgroupCabinetManagementReportWgcStatusesState = 'workgroup_cabinet_management_report_wgc_statuses_state',
  WorkgroupCabinetManagementReportQuotasReserves = 'workgroup_cabinet_management_report_quotas_reserves',
  WorkgroupCabinetNotificationListReportParticipantInteraction = 'workgroup_cabinet_notification_list_report_participant_interaction',
  WorkgroupCabinetReportEpcDocuments = 'workgroup_cabinet_report_epc_documents',
  WorkgroupCabinetReportEpcDocumentsWide = 'workgroup_cabinet_report_epc_documents_wide',
  WorkgroupCabinetReportEpcEnclosedDocuments = 'workgroup_cabinet_report_epc_enclosed_documents',
  WorkgroupCabinetReportExecutedDocuments = 'workgroup_cabinet_report_executed_documents',
  WorkgroupCabinetReportEpcOutboxDocuments = 'workgroup_cabinet_report_epc_outbox_documents',
  WorkgroupCabinetReportEpcInboxDocuments = 'workgroup_cabinet_report_epc_inbox_documents',
  WorkgroupCabinetReportEpcDocumentsSelection = 'workgroup_cabinet_report_epc_documents_selection',
  WorkgroupCabinetReportOutboxDocuments = 'workgroup_cabinet_report_outbox_documents',
  WorkgroupCabinetReportInboxDocuments = 'workgroup_cabinet_report_inbox_documents',
  WorkgroupCabinetManagementWgcOperations = 'workgroup_cabinet_management_wgc_operations',
}

export enum WildcardPermissions {
  FullAccess = 'wildcard_epc_full_access_name_key',
  PrintAccess = 'wildcard_file_print_pl_name_key',
  CopyAccess = 'wildcard_file_copy_name_key',
  OkatoAccess = 'wildcard_okato_12_name_key',
  FileDownloadPl = 'wildcard_file_download_pl_name_key',
  AuditQuestion = 'wildcard_audit_question_name_key',
  ActivityKind = 'wildcard_activity_kind_name_key',
  FilePrint = 'wildcard_file_print_name_key',
  FileType = 'wildcard_file_type_name_key',
  AuditQuestionPl = 'wildcard_audit_question_pl_name_key',
  FileDownloadKm = 'wildcard_file_download_km_name_key',
  Inspection = 'wildcard_inspection_name_key',
  DateRangePl = 'wildcard_date_range_pl_name_key',
  OkatoPl = 'wildcard_okato_pl_name_key',
  FileTypeNfo = 'wildcard_file_type_nfo_name_key',
  CheckListPl = 'wildcard_check_list_pl_name_key',
  FilePrintNfo = 'wildcard_file_print_nfo_name_key',
  OperRight = 'wildcard_oper_right_name_key',
  AuditQuestionNfo = 'wildcard_audit_question_nfo_name_key',
  FileCopyPl = 'wildcard_file_copy_pl_name_key',
  FileTypePl = 'wildcard_file_type_pl_name_key',
  FileCopyNfo = 'wildcard_file_copy_nfo_name_key',
  FileSearch = 'wildcard_file_search_name_key',
  CheckListNfo = 'wildcard_check_list_nfo_name_key',
  DateRangeNfo = 'wildcard_date_range_nfo_name_key',
  OkatoNfo = 'wildcard_okato_nfo_name_key',
  ReportGet = 'wildcard_report_get_name_key',
  DateRange = 'wildcard_date_range_name_key',
  FileDownload = 'wildcard_file_download_name_key',
  CheckList = 'wildcard_check_list_name_key',
  FileDownloadNfo = 'wildcard_file_download_nfo_name_key',
}
