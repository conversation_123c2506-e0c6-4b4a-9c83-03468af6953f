import { apiUrls, appInstance } from 'shared/api';
import { appErrorNotification } from 'shared/lib';
import { specialPermissionsStore } from '..';

export const canDownloadReport = async (
  reportNameKey: string | undefined,
): Promise<boolean> => {
  try {
    const { data: downloadPermissions } = await appInstance.get<
      specialPermissionsStore.enums.ReportPermissions[]
    >(apiUrls.reportPermissions);

    return downloadPermissions.includes(
      reportNameKey as specialPermissionsStore.enums.ReportPermissions,
    );
  } catch (err) {
    appErrorNotification('Ошибка получения прав для отчетов', err as AppError);
  }

  return false;
};
