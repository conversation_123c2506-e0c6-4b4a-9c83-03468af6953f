import { useCallback, useState } from 'react';
import { useEffectOnce } from 'react-use';
import { RubricElement } from 'features/FilenetInfoCards/types';
import { apiUrls, filenetServiceInstance } from 'shared/api';
import { useAxiosRequest } from 'shared/model';

type SetCallback = (selectedTab: string) => void;
type TabsInfo = {
  selectedTab: string;
  tabs: string[];
};

export const useDossierTabs = (
  popupId: string | number,
): [TabsInfo, SetCallback, typeof rubricsStatuses] => {
  const [triggerRubrics, rubricsStatuses] = useAxiosRequest<RubricElement[]>(
    filenetServiceInstance,
  );

  const [tabsInfo, setTabsInfo] = useState<TabsInfo>({
    tabs: [],
    selectedTab: '',
  });

  const setSelectedTab = useCallback<SetCallback>((selectedTab) => {
    setTabsInfo((prev) => ({
      ...prev,
      selectedTab,
    }));
  }, []);

  const getAndSetData: PromiseCallback = async () => {
    const resTabs = await triggerRubrics(apiUrls.fileNet.dossierRubrics, {
      method: 'POST',
      data: { ids: [popupId] },
    });

    if (resTabs.length > 0) {
      setTabsInfo({
        tabs: resTabs.map(({ externalId }) => externalId),
        selectedTab: resTabs[0].externalId,
      });
    }
  };

  useEffectOnce(() => {
    getAndSetData();
  });

  return [tabsInfo, setSelectedTab, rubricsStatuses];
};
