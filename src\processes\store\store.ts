import { configureStore } from '@reduxjs/toolkit';
import { setupListeners } from '@reduxjs/toolkit/query';

import { controlAndPlansStore } from 'pages/ControlAndPlans';
import { FileNetStore } from 'pages/FileNet';
import { fileNetReportStore } from 'pages/FileNetReport';
import { fileNetSyncStore } from 'pages/FileNetSync';
import { permissionControlsStore } from 'pages/PermissionControls';
import { DirOfOrgCardStore } from 'widgets/DirectoryOfOrganizationCard';
import { filtersDrawerStore } from 'widgets/FiltersDrawer';
import { organizationalStructuresStore } from 'widgets/OrganizationalStructures';
import { permissionModalsStore } from 'widgets/PermissionModals';
import { ReplicatorLogStore } from 'widgets/ReplicatorLog';
import { ReplicatorSettingsStore } from 'widgets/ReplicatorSettings';
import { sideBarStore } from 'widgets/SideBar';
import { userNotificationTabStore } from 'widgets/UserNotificationTab';
import { UserSubscriptionEditStore } from 'widgets/UserSubscriptionEdit';
import { UserSubscriptionsTabStore } from 'widgets/UserSubscriptionsTab';
import { wgFileSearchStore } from 'widgets/WGFileSearch';
import { WorkGroupCatalogsStore } from 'widgets/WorkGroupCatalogs';
import { WorkGroupCompositionStore } from 'widgets/WorkGroupComposition';
import { WGCNotificationStore } from 'widgets/WorkGroupControlNotification';
import { workGroupCreationStore } from 'widgets/WorkGroupCreation';
import { WorkGroupCreationPlanStore } from 'widgets/WorkGroupCreationPlan';
import { WorkGroupDefFilesStore } from 'widgets/WorkGroupDefFiles';
import { WorkGroupEPCStore } from 'widgets/WorkGroupEPC';
import { WorkGroupInspectionStore } from 'widgets/WorkGroupInspection';
import { WorkGroupPassportStore } from 'widgets/WorkGroupPassport';
import { WorkGroupRequestStore } from 'widgets/WorkGroupRequest';
import { WorkGroupSettingsStore } from 'widgets/WorkGroupSettings';
import { workGroupUploadStore } from 'widgets/WorkGroupUpload';
import { filenetDossierStructureStore } from 'features/FilenetDossierStructure';
import { filenetSearchStore } from 'features/FilenetSearch';
import { newlazyTree } from 'features/NewLazyTree';
import { appPopupStore } from 'entities/AppPopup';
import { blitzStore } from 'entities/Blitz';
import { indexFilesStore } from 'entities/IndexFilesTable';
import { loadingStatusStore } from 'entities/LoadingStatus';
import { permissionsStore } from 'entities/Permissions';
import { specialPermissionsStore } from 'entities/SpecialPermissions';
import { userInfoStore } from 'entities/UserInfo';

import { middlewares } from './middlewares';

const reducer = {
  [DirOfOrgCardStore.reducers.slice.name]:
    DirOfOrgCardStore.reducers.slice.reducer,
  [controlAndPlansStore.reducers.slice.name]:
    controlAndPlansStore.reducers.slice.reducer,
  [FileNetStore.reducers.slice.name]: FileNetStore.reducers.slice.reducer,
  [filenetDossierStructureStore.reducers.slice.name]:
    filenetDossierStructureStore.reducers.slice.reducer,
  [filenetSearchStore.reducers.slice.name]:
    filenetSearchStore.reducers.slice.reducer,
  [newlazyTree.reducers.slice.name]: newlazyTree.reducers.slice.reducer,
  [fileNetSyncStore.reducers.slice.name]:
    fileNetSyncStore.reducers.slice.reducer,
  [fileNetReportStore.reducers.slice.name]:
    fileNetReportStore.reducers.slice.reducer,
  [userNotificationTabStore.reducers.slice.name]:
    userNotificationTabStore.reducers.slice.reducer,
  [UserSubscriptionsTabStore.reducers.slice.name]:
    UserSubscriptionsTabStore.reducers.slice.reducer,
  [UserSubscriptionEditStore.reducers.slice.name]:
    UserSubscriptionEditStore.reducers.slice.reducer,
  [organizationalStructuresStore.reducers.slice.name]:
    organizationalStructuresStore.reducers.slice.reducer,
  [permissionControlsStore.reducers.slice.name]:
    permissionControlsStore.reducers.slice.reducer,
  [permissionModalsStore.reducers.slice.name]:
    permissionModalsStore.reducers.slice.reducer,
  [userInfoStore.reducers.slice.name]: userInfoStore.reducers.slice.reducer,
  [blitzStore.reducers.slice.name]: blitzStore.reducers.slice.reducer,
  [filtersDrawerStore.reducers.slice.name]:
    filtersDrawerStore.reducers.slice.reducer,
  [ReplicatorSettingsStore.reducers.slice.name]:
    ReplicatorSettingsStore.reducers.slice.reducer,
  [ReplicatorLogStore.reducers.slice.name]:
    ReplicatorLogStore.reducers.slice.reducer,
  [WorkGroupCompositionStore.reducers.slice.name]:
    WorkGroupCompositionStore.reducers.slice.reducer,

  [WorkGroupSettingsStore.reducers.slice.name]:
    WorkGroupSettingsStore.reducers.slice.reducer,
  [WorkGroupInspectionStore.reducers.slice.name]:
    WorkGroupInspectionStore.reducers.slice.reducer,
  [WorkGroupCatalogsStore.reducers.slice.name]:
    WorkGroupCatalogsStore.reducers.slice.reducer,
  [WorkGroupCreationPlanStore.reducers.slice.name]:
    WorkGroupCreationPlanStore.reducers.slice.reducer,

  [WorkGroupEPCStore.reducers.slice.name]:
    WorkGroupEPCStore.reducers.slice.reducer,
  [WorkGroupPassportStore.reducers.slice.name]:
    WorkGroupPassportStore.reducers.slice.reducer,
  [WorkGroupRequestStore.reducers.slice.name]:
    WorkGroupRequestStore.reducers.slice.reducer,
  [workGroupCreationStore.reducers.slice.name]:
    workGroupCreationStore.reducers.slice.reducer,

  [WGCNotificationStore.reducers.slice.name]:
    WGCNotificationStore.reducers.slice.reducer,

  [sideBarStore.reducers.slice.name]: sideBarStore.reducers.slice.reducer,

  [workGroupUploadStore.reducers.slice.name]:
    workGroupUploadStore.reducers.slice.reducer,
  appPopup: appPopupStore.reducer,
  [WorkGroupDefFilesStore.reducers.slice.name]:
    WorkGroupDefFilesStore.reducers.slice.reducer,
  [wgFileSearchStore.reducers.slice.name]:
    wgFileSearchStore.reducers.slice.reducer,

  [indexFilesStore.reducers.slice.name]: indexFilesStore.reducers.slice.reducer,
  [permissionsStore.reducers.slice.name]:
    permissionsStore.reducers.slice.reducer,
  [specialPermissionsStore.reducers.slice.name]:
    specialPermissionsStore.reducers.slice.reducer,
  [loadingStatusStore.reducers.slice.name]:
    loadingStatusStore.reducers.slice.reducer,
};

export const store = configureStore({
  reducer,
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      immutableCheck: false,
      serializableCheck: false,
    }).concat(middlewares),
  devTools: process.env.NODE_ENV !== 'production',
});

setupListeners(store.dispatch);

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
export type RootStore = typeof store;
export type AppSelector<Return> = (state: RootState) => Return;
