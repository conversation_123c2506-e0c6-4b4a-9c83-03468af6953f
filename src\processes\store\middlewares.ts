import { isRejected, isRejectedWithValue } from '@reduxjs/toolkit';
import type { AnyAction, Dispatch, Middleware } from '@reduxjs/toolkit';
import axios from 'axios';
import { createLogger } from 'redux-logger';

import { isDevEnv } from 'shared/config';
import { errorMessages } from 'shared/config/errorMessages';
import { appErrorNotification } from 'shared/lib';
import { ErrorWithoutShow } from 'shared/model';

export const middlewares: Middleware<
  Record<string, unknown>,
  unknown,
  Dispatch<AnyAction>
>[] = [];

const blacklist = ['__rtkq/'];

if (
  process.env.NODE_ENV !== 'production' &&
  localStorage.getItem('REDUX-DEBUG') !== null
) {
  const logger = createLogger({
    predicate: (getState, action) =>
      !blacklist.some((type) => action.type.startsWith(type)),
  });
  middlewares.push(logger);
}

/** Централизованная мидлвара для обработки ошибок с асинхронных запросов */
const rtkQueryErrorLogger: Middleware = () => (next) => (action) => {
  try {
    if (isRejected(action) || isRejectedWithValue(action)) {
      /* Показ ошибок только на на деве */
      if (isDevEnv) {
        console.groupCollapsed(`rtkQueryErrorLogger [${action.type}]`); // eslint-disable-line no-console
        console.warn('We got a rejected action!'); // eslint-disable-line no-console
        console.log(action); // eslint-disable-line no-console

        if (axios.isAxiosError(action.payload)) {
          console.groupCollapsed('axiosError'); // eslint-disable-line no-console
          console.log(action.payload.toJSON()); // eslint-disable-line no-console
          console.groupEnd(); // eslint-disable-line no-console
        }

        console.groupEnd(); // eslint-disable-line no-console
      }

      const isAxiosError = axios.isAxiosError(action.payload);

      if (
        /** Проверка на экшен авторизации */
        action.type.includes('handleLogin') ||
        /** Проверка на экшн проверки прав */
        action.type === 'sideBar/getSideBarButtonsThunk/rejected' ||
        /** Проверка на отмену запроса */
        action.meta.aborted ||
        /** Проверка на ошибку с отменой запроса */
        action.error instanceof ErrorWithoutShow ||
        action.payload === 'Canceled' ||
        action.error?.message === 'Canceled' ||
        /* Проверка на ошибку кэша RTK query */
        action?.error.message ===
          'Aborted due to condition callback returning false.' ||
        /* Проверка на ошибку, связанную с бизнес логикой */
        (isAxiosError && action.payload?.response?.data?.isBusinessLogicError)
      ) {
        return;
      }

      appErrorNotification(
        `${errorMessages.pendingError}. Тип запроса: ${action.type.replace(
          'Thunk/rejected',
          '',
        )}`,
        isAxiosError ? action.payload : action.error,
      );
    }
  } catch (error) {
    /* Показ ошибок только на деве */
    if (isDevEnv) {
      // eslint-disable-next-line no-console
      console.error(error);
    }
  } finally {
    next(action);
  }
};

middlewares.push(rtkQueryErrorLogger);
