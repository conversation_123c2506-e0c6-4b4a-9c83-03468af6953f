export const workGroupSelect = [
  {
    title:
      'Опись документов, размещенных в электронном паспорте проверки поднадзорного лица (сокращенный вариант)',
    key: '1',
    typeNameKey: 'workgroup_cabinet_report_epc_documents',
  },
  {
    title:
      'Опись документов, размещенных в электронном паспорте проверки поднадзорного лица (полный вариант)',
    key: '2',
    typeNameKey: 'workgroup_cabinet_report_epc_documents_wide',
  },
  {
    title:
      'Опись документов, приобщенных в электронную составляющую паспорта проверки',
    key: '3',
    typeNameKey: 'workgroup_cabinet_report_epc_enclosed_documents',
  },
  {
    title: 'Сведения о сформированных РГ и исполненных документах',
    key: '4',
    typeNameKey: 'workgroup_cabinet_report_executed_documents',
  },
  {
    title:
      'Сведения об исходящих документах, включаемых в ЭПП, в том числе сформированных РГ',
    key: '5',
    typeNameKey: 'workgroup_cabinet_report_epc_outbox_documents',
  },
  {
    title:
      'Сведения о входящих документах, размещаемых в ЭПП, в том числе сформированных ПЛ',
    key: '6',
    typeNameKey: 'workgroup_cabinet_report_epc_inbox_documents',
  },
  {
    title:
      'Выборка документов из электронного паспорта проверки поднадзорного лица',
    key: '7',
    typeNameKey: 'workgroup_cabinet_report_epc_documents_selection',
  },
  {
    title: 'Сведения об исходящих документах',
    key: '8',
    typeNameKey: 'workgroup_cabinet_report_outbox_documents',
  },
  {
    title: 'Сведения о входящих документах',
    key: '9',
    typeNameKey: 'workgroup_cabinet_report_inbox_documents',
  },
] as const;
